import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler, RobustScaler, QuantileTransformer
from sklearn.neighbors import KNeighborsClassifier
import warnings
import os
from collections import Counter
from scipy import stats
from scipy.spatial.distance import cdist

warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False


class TransferLearningDiagnosisSystem:
    """迁移学习诊断系统 - 基于源域随机森林模型的深度域适应分析"""

    def __init__(self, source_data_path='processed_data_mixed_fs', target_data_path='processed_target_data'):
        self.source_data_path = source_data_path
        self.target_data_path = target_data_path
        self.source_features = None
        self.source_labels = None
        self.target_features = None
        self.target_df = None
        self.feature_names = None
        self.source_rf_model = None  # 从任务2加载的源域模型
        self.domain_adapted_models = {}
        self.final_predictions = {}

        # 目标故障类型
        self.target_fault_types = ['IR', 'OR', 'B']

        # 域适应分析结果
        self.domain_gap_analysis = {}
        self.adaptation_results = {}

    def load_source_model_and_data(self):
        """加载任务2训练的源域随机森林模型和数据"""
        print("=" * 60)
        print("加载源域模型和数据")
        print("=" * 60)

        # 1. 加载源域数据
        print("1. 加载源域数据...")
        source_feature_file = f"{self.source_data_path}/extracted_features.csv"
        if not os.path.exists(source_feature_file):
            raise FileNotFoundError(f"源域特征文件不存在: {source_feature_file}")

        source_df = pd.read_csv(source_feature_file)
        print(f"源域原始数据: {source_df.shape}")

        # 只保留目标故障类型
        fault_mask = source_df['fault_type'].isin(self.target_fault_types)
        source_df = source_df[fault_mask]

        # 提取特征
        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']
        self.feature_names = [col for col in source_df.columns if col not in exclude_cols]

        self.source_features = source_df[self.feature_names].values
        self.source_labels = source_df['fault_type'].values

        self.source_features = self._clean_data(self.source_features)
        self.scaler = QuantileTransformer(random_state=42)
        self.source_features = self.scaler.fit_transform(self.source_features)

        print("\n2. 训练源域随机森林模型...")
        self.source_rf_model = RandomForestClassifier(
            n_estimators=300,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            class_weight='balanced',
            n_jobs=-1
        )

        self.source_rf_model.fit(self.source_features, self.source_labels)

        # 源域性能评估
        source_pred = self.source_rf_model.predict(self.source_features)
        source_acc = accuracy_score(self.source_labels, source_pred)
        print(f"源域模型准确率: {source_acc:.4f}")

        print(f"源域模型训练完成: 特征数={len(self.feature_names)}, 样本数={len(self.source_features)}")
        return self.source_rf_model

    def load_target_domain_data(self):
        """加载目标域数据"""
        print("\n3. 加载目标域数据...")

        target_file = os.path.join(self.target_data_path, 'target_features_compatible.csv')
        if not os.path.exists(target_file):
            target_file = os.path.join(self.target_data_path, 'target_features.csv')

        if not os.path.exists(target_file):
            raise FileNotFoundError("未找到目标域特征文件")

        self.target_df = pd.read_csv(target_file)
        print(f"目标域数据: {self.target_df.shape}")

        # 特征对齐
        target_exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs',
                               'resampled_fs', 'source_file', 'sample_index', 'file_name']
        available_cols = [col for col in self.target_df.columns if col not in target_exclude_cols]

        # 补充缺失特征
        for feature in self.feature_names:
            if feature not in available_cols:
                self.target_df[feature] = 0.0

        self.target_features = self.target_df[self.feature_names].values
        self.target_features = self._clean_data(self.target_features)
        self.target_features = self.scaler.transform(self.target_features)

        if 'source_file' in self.target_df.columns:
            target_files = sorted(self.target_df['source_file'].unique())
            print(f"目标域文件: {len(target_files)} 个")

        print(f"目标域数据处理完成: {self.target_features.shape}")
        return self.target_features

    def _clean_data(self, data):
        """数据清理"""
        data_cleaned = data.copy()

        # 处理NaN和无穷值
        for i in range(data.shape[1]):
            col_data = data[:, i]
            finite_mask = np.isfinite(col_data)

            if not np.all(finite_mask):
                median_val = np.median(col_data[finite_mask]) if np.any(finite_mask) else 0.0
                data_cleaned[~finite_mask, i] = median_val

            if np.any(finite_mask) and np.std(col_data[finite_mask]) > 0:
                mean_val = np.mean(col_data[finite_mask])
                std_val = np.std(col_data[finite_mask])
                outlier_mask = np.abs(col_data - mean_val) > 3 * std_val

                if np.any(outlier_mask):
                    p95 = np.percentile(col_data[finite_mask], 95)
                    p05 = np.percentile(col_data[finite_mask], 5)
                    data_cleaned[outlier_mask & (col_data > mean_val), i] = p95
                    data_cleaned[outlier_mask & (col_data < mean_val), i] = p05

        return np.nan_to_num(data_cleaned, nan=0.0, posinf=1e10, neginf=-1e10)

    def deep_domain_gap_analysis(self):
        """深度域间差异分析"""
        print("\n" + "=" * 60)
        print("深度域间差异分析")
        print("=" * 60)

        # 1. MMD距离计算
        print("1. 计算MMD距离...")
        mmd_distance = self._compute_mmd_distance(self.source_features, self.target_features)
        print(f"   MMD距离: {mmd_distance:.6f}")

        # 2. 统计差异分析
        print("2. 统计差异分析...")
        statistical_differences = self._analyze_statistical_differences()

        # 3. 分布重叠度计算
        print("3. 分布重叠度分析...")
        overlap_analysis = self._analyze_distribution_overlap()

        # 4. 特征稳定性分析
        print("4. 特征稳定性分析...")
        feature_stability = self._analyze_feature_stability()

        self.domain_gap_analysis = {
            'mmd_distance': mmd_distance,
            'statistical_differences': statistical_differences,
            'overlap_analysis': overlap_analysis,
            'feature_stability': feature_stability
        }

        print(f"域间差异分析完成")
        return self.domain_gap_analysis

    def _compute_mmd_distance(self, source_data, target_data, gamma=1.0):
        """计算MMD (Maximum Mean Discrepancy) 距离"""

        # 使用RBF核的MMD距离
        def rbf_kernel(X, Y, gamma):
            pairwise_dists = cdist(X, Y, 'sqeuclidean')
            return np.exp(-gamma * pairwise_dists)

        n_samples = min(1000, len(source_data), len(target_data))
        source_sample = source_data[np.random.choice(len(source_data), n_samples, replace=False)]
        target_sample = target_data[np.random.choice(len(target_data), n_samples, replace=False)]

        K_ss = rbf_kernel(source_sample, source_sample, gamma)
        K_tt = rbf_kernel(target_sample, target_sample, gamma)
        K_st = rbf_kernel(source_sample, target_sample, gamma)

        mmd_squared = (np.mean(K_ss) + np.mean(K_tt) - 2 * np.mean(K_st))
        return np.sqrt(max(0, mmd_squared))

    def _analyze_statistical_differences(self):
        """统计差异分析"""
        differences = []

        for i, feature_name in enumerate(self.feature_names):
            source_feat = self.source_features[:, i]
            target_feat = self.target_features[:, i]

            # KS检验
            ks_stat, ks_pvalue = stats.ks_2samp(source_feat, target_feat)

            # Wasserstein距离
            wasserstein_dist = stats.wasserstein_distance(source_feat, target_feat)

            # 均值和方差差异
            mean_diff = abs(np.mean(source_feat) - np.mean(target_feat))
            var_ratio = np.var(target_feat) / (np.var(source_feat) + 1e-8)

            differences.append({
                'feature_name': feature_name,
                'ks_statistic': ks_stat,
                'ks_pvalue': ks_pvalue,
                'wasserstein_distance': wasserstein_dist,
                'mean_difference': mean_diff,
                'variance_ratio': var_ratio
            })

        differences.sort(key=lambda x: x['ks_statistic'], reverse=True)

        print(f"   分布差异最大的5个特征:")
        for i, diff in enumerate(differences[:5]):
            print(f"     {i + 1}. {diff['feature_name'][:30]:30s}: KS={diff['ks_statistic']:.4f}")

        return differences

    def _analyze_distribution_overlap(self):
        """分布重叠度分析"""
        overlap_scores = []

        for i in range(len(self.feature_names)):
            source_feat = self.source_features[:, i]
            target_feat = self.target_features[:, i]

            s_q25, s_q75 = np.percentile(source_feat, [25, 75])
            t_q25, t_q75 = np.percentile(target_feat, [25, 75])

            overlap_start = max(s_q25, t_q25)
            overlap_end = min(s_q75, t_q75)

            s_range = s_q75 - s_q25
            t_range = t_q75 - t_q25

            if overlap_end > overlap_start and s_range > 0 and t_range > 0:
                overlap_length = overlap_end - overlap_start
                overlap_ratio = overlap_length / min(s_range, t_range)
            else:
                overlap_ratio = 0.0

            overlap_scores.append(overlap_ratio)

        avg_overlap = np.mean(overlap_scores)
        print(f"   平均分布重叠度: {avg_overlap:.4f}")

        return {
            'feature_overlaps': overlap_scores,
            'average_overlap': avg_overlap,
            'low_overlap_features': [self.feature_names[i] for i, score in enumerate(overlap_scores) if score < 0.3]
        }

    def _analyze_feature_stability(self):
        """特征稳定性分析"""
        # 使用源域模型的特征重要性
        rf_importance = self.source_rf_model.feature_importances_

        # 计算特征在目标域的变异系数
        target_cv = []
        for i in range(len(self.feature_names)):
            feat_std = np.std(self.target_features[:, i])
            feat_mean = np.mean(self.target_features[:, i])
            cv = feat_std / (abs(feat_mean) + 1e-8)
            target_cv.append(cv)

        # 结合重要性和稳定性的综合评分
        stability_scores = []
        for i in range(len(self.feature_names)):
            importance = rf_importance[i]
            cv = target_cv[i]
            # 重要性高且变异小的特征更稳定
            stability = importance / (1 + cv)
            stability_scores.append(stability)

        # 选择最稳定的特征
        stable_indices = np.argsort(stability_scores)[-20:]  # 前20个最稳定特征

        print(f"   选择了 {len(stable_indices)} 个最稳定的特征")

        return {
            'stability_scores': stability_scores,
            'stable_feature_indices': stable_indices,
            'stable_features': [self.feature_names[i] for i in stable_indices]
        }

    def multi_strategy_domain_adaptation(self):
        """多策略域适应"""
        print("\n" + "=" * 60)
        print("多策略域适应")
        print("=" * 60)

        adapted_models = {}

        # 策略1: CORAL二阶统计量对齐
        print("1. CORAL二阶统计量对齐...")
        coral_source, coral_target = self._coral_alignment()
        coral_model = self._train_adapted_model(coral_source, coral_target, "CORAL")
        adapted_models['coral'] = coral_model

        # 策略2: 批归一化域适应
        print("2. 批归一化域适应...")
        bn_model = self._batch_normalization_adaptation()
        adapted_models['batch_norm'] = bn_model

        # 策略3: 基于特征稳定性的特征选择
        print("3. 特征稳定性选择...")
        stable_model = self._feature_stability_adaptation()
        adapted_models['feature_stable'] = stable_model

        # 策略4: 原始模型作为基线
        adapted_models['original'] = self.source_rf_model

        self.domain_adapted_models = adapted_models
        print(f"域适应完成，共 {len(adapted_models)} 个模型")
        return adapted_models

    def _coral_alignment(self):
        """CORAL (CORrelation ALignment) 二阶统计量对齐"""
        # 计算源域和目标域的协方差矩阵
        source_cov = np.cov(self.source_features.T)
        target_cov = np.cov(self.target_features.T)

        # 添加正则化项避免奇异矩阵
        reg_param = 1e-3
        source_cov += reg_param * np.eye(source_cov.shape[0])
        target_cov += reg_param * np.eye(target_cov.shape[0])

        # 计算变换矩阵
        try:
            # 使用Cholesky分解
            source_chol = np.linalg.cholesky(source_cov)
            target_chol = np.linalg.cholesky(target_cov)

            # 计算变换矩阵
            source_inv_chol = np.linalg.inv(source_chol)
            transform_matrix = target_chol @ source_inv_chol

            # 应用变换
            coral_source = self.source_features @ transform_matrix.T
            coral_target = self.target_features @ transform_matrix.T

        except np.linalg.LinAlgError:
            print("     使用SVD分解进行CORAL对齐")
            U_s, s_s, Vt_s = np.linalg.svd(source_cov)
            U_t, s_t, Vt_t = np.linalg.svd(target_cov)

            sqrt_source = U_s @ np.diag(np.sqrt(s_s + reg_param)) @ Vt_s
            sqrt_target = U_t @ np.diag(np.sqrt(s_t + reg_param)) @ Vt_t

            transform_matrix = np.linalg.pinv(sqrt_source) @ sqrt_target
            coral_source = self.source_features @ transform_matrix.T
            coral_target = self.target_features @ transform_matrix.T

        print(f"     CORAL对齐完成")
        return coral_source, coral_target

    def _train_adapted_model(self, adapted_source, adapted_target, strategy_name):
        """训练域适应后的模型"""
        # 使用适应后的源域数据训练模型
        adapted_model = RandomForestClassifier(
            n_estimators=200,
            max_depth=12,
            min_samples_split=5,
            random_state=42,
            class_weight='balanced'
        )

        adapted_model.fit(adapted_source, self.source_labels)

        # 在适应后的目标域上评估（用于调试）
        if len(adapted_target) > 0:
            pred = adapted_model.predict(adapted_target[:100])
            print(f"     {strategy_name}模型训练完成")

        return adapted_model

    def _batch_normalization_adaptation(self):
        """批归一化域适应"""
        source_mean = np.mean(self.source_features, axis=0)
        source_std = np.std(self.source_features, axis=0) + 1e-8

        target_mean = np.mean(self.target_features, axis=0)
        target_std = np.std(self.target_features, axis=0) + 1e-8

        norm_source = (self.source_features - source_mean) / source_std
        adapted_source = norm_source * target_std + target_mean
        bn_model = RandomForestClassifier(
            n_estimators=200,
            max_depth=12,
            random_state=42,
            class_weight='balanced'
        )

        bn_model.fit(adapted_source, self.source_labels)
        print(f"     批归一化适应完成")
        return bn_model

    def _feature_stability_adaptation(self):
        """基于特征稳定性的适应"""
        stable_indices = self.domain_gap_analysis['feature_stability']['stable_feature_indices']

        # 只使用稳定特征训练模型
        stable_source = self.source_features[:, stable_indices]

        stable_model = RandomForestClassifier(
            n_estimators=250,
            max_depth=15,
            random_state=42,
            class_weight='balanced'
        )

        stable_model.fit(stable_source, self.source_labels)
        print(f"     特征稳定性适应完成")
        return stable_model

    def ensemble_prediction_with_soft_voting(self):
        """集成预测与软投票"""
        print("\n" + "=" * 60)
        print("集成预测与软投票")
        print("=" * 60)

        # 收集每个模型的预测结果
        model_predictions = {}
        model_confidences = {}

        for strategy_name, model in self.domain_adapted_models.items():
            print(f"执行 {strategy_name} 模型预测...")

            if strategy_name == 'feature_stable':
                # 特征稳定性模型只使用稳定特征
                stable_indices = self.domain_gap_analysis['feature_stability']['stable_feature_indices']
                target_data = self.target_features[:, stable_indices]
            elif strategy_name == 'coral':
                # CORAL模型需要使用对齐后的数据
                _, coral_target = self._coral_alignment()
                target_data = coral_target
            elif strategy_name == 'batch_norm':
                # 批归一化适应
                source_mean = np.mean(self.source_features, axis=0)
                source_std = np.std(self.source_features, axis=0) + 1e-8
                target_mean = np.mean(self.target_features, axis=0)
                target_std = np.std(self.target_features, axis=0) + 1e-8
                norm_target = (self.target_features - target_mean) / target_std
                target_data = norm_target * source_std + source_mean
            else:
                # 原始模型
                target_data = self.target_features

            # 预测
            try:
                predictions = model.predict(target_data)
                probabilities = model.predict_proba(target_data)
                confidences = np.max(probabilities, axis=1)

                model_predictions[strategy_name] = predictions
                model_confidences[strategy_name] = confidences

                pred_dist = Counter(predictions)
                avg_conf = np.mean(confidences)
                print(f"   {strategy_name}: 平均置信度={avg_conf:.3f}, 分布={dict(pred_dist)}")

            except Exception as e:
                print(f"   {strategy_name} 预测失败: {e}")
                continue

        # 软投票集成
        print("\n执行软投票集成...")
        ensemble_predictions, ensemble_confidences = self._soft_voting_ensemble(
            model_predictions, model_confidences
        )

        return ensemble_predictions, ensemble_confidences

    def _soft_voting_ensemble(self, model_predictions, model_confidences):
        """软投票集成"""
        if not model_predictions:
            return [], []

        n_samples = len(list(model_predictions.values())[0])
        ensemble_predictions = []
        ensemble_confidences = []

        model_weights = {
            'coral': 0.3,
            'batch_norm': 0.25,
            'feature_stable': 0.25,
            'original': 0.2
        }

        for i in range(n_samples):
            sample_votes = {}
            sample_confidences = {}

            for strategy_name in model_predictions:
                if i < len(model_predictions[strategy_name]):
                    pred = model_predictions[strategy_name][i]
                    conf = model_confidences[strategy_name][i]
                    weight = model_weights.get(strategy_name, 0.2)

                    if pred not in sample_votes:
                        sample_votes[pred] = 0
                        sample_confidences[pred] = []

                    sample_votes[pred] += weight * conf
                    sample_confidences[pred].append(conf)

            # 选择得票最高的预测
            if sample_votes:
                best_prediction = max(sample_votes.keys(), key=lambda x: sample_votes[x])
                best_confidence = np.mean(sample_confidences[best_prediction])

                ensemble_predictions.append(best_prediction)
                ensemble_confidences.append(best_confidence)
            else:
                ensemble_predictions.append('IR')
                ensemble_confidences.append(0.3)

        return ensemble_predictions, ensemble_confidences

    def knn_optimization_for_low_confidence(self, predictions, confidences, threshold=0.6):

        low_conf_mask = np.array(confidences) < threshold
        low_conf_indices = np.where(low_conf_mask)[0]

        if len(low_conf_indices) == 0:
            print("   没有低置信度样本需要优化")
            return predictions, confidences
        knn_model = KNeighborsClassifier(n_neighbors=7, weights='distance')
        knn_model.fit(self.source_features, self.source_labels)

        optimized_predictions = predictions.copy()
        optimized_confidences = confidences.copy()

        for idx in low_conf_indices:
            sample = self.target_features[idx].reshape(1, -1)
            knn_pred = knn_model.predict(sample)[0]
            knn_proba = knn_model.predict_proba(sample)[0]
            knn_conf = np.max(knn_proba)

            # 如果KNN给出更高置信度，则采用KNN结果
            if knn_conf > confidences[idx]:
                optimized_predictions[idx] = knn_pred
                optimized_confidences[idx] = min(knn_conf, 0.85)  # 限制最大置信度

        improved_count = sum(1 for i in low_conf_indices
                             if optimized_confidences[i] > confidences[i])
        print(f"   优化了 {improved_count} 个样本的预测")

        return optimized_predictions, optimized_confidences

    def file_level_aggregation_and_prediction(self):
        """文件级聚合和预测"""
        print("\n" + "=" * 60)
        print("文件级聚合和最终预测")
        print("=" * 60)

        # 1. 获取集成预测结果
        sample_predictions, sample_confidences = self.ensemble_prediction_with_soft_voting()

        # 2. KNN优化
        optimized_predictions, optimized_confidences = self.knn_optimization_for_low_confidence(
            sample_predictions, sample_confidences, threshold=0.6
        )

        # 3. 文件级聚合
        print("\n执行文件级聚合...")
        file_predictions = {}

        if 'source_file' in self.target_df.columns:
            target_files = sorted(self.target_df['source_file'].unique())

            for file_name in target_files:
                file_mask = self.target_df['source_file'] == file_name
                file_indices = np.where(file_mask)[0]

                if len(file_indices) == 0:
                    continue

                file_preds = [optimized_predictions[i] for i in file_indices
                              if i < len(optimized_predictions)]
                file_confs = [optimized_confidences[i] for i in file_indices
                              if i < len(optimized_confidences)]

                if not file_preds:
                    continue

                weighted_votes = {}
                for pred, conf in zip(file_preds, file_confs):
                    if pred not in weighted_votes:
                        weighted_votes[pred] = 0
                    weighted_votes[pred] += conf

                final_pred = max(weighted_votes.keys(), key=lambda x: weighted_votes[x])

                pred_mask = np.array(file_preds) == final_pred
                final_conf = np.mean([file_confs[i] for i in range(len(file_confs)) if pred_mask[i]])

                vote_ratio = sum(1 for p in file_preds if p == final_pred) / len(file_preds)
                if vote_ratio > 0.8:
                    final_conf = min(0.95, final_conf + 0.1)

                file_predictions[file_name] = {
                    'prediction': final_pred,
                    'confidence': final_conf,
                    'sample_count': len(file_indices),
                    'vote_ratio': vote_ratio,
                    'sample_predictions': file_preds,
                    'sample_confidences': file_confs
                }

                print(f"   {file_name}: {final_pred} (置信度: {final_conf:.3f}, 投票率: {vote_ratio:.2f})")

        else:
            # 如果没有文件信息，按顺序分组
            print("   没有文件信息，使用默认分组")
            n_files = 16
            samples_per_file = len(optimized_predictions) // n_files

            for i in range(n_files):
                file_name = f"{chr(65 + i)}.mat"
                start_idx = i * samples_per_file
                end_idx = start_idx + samples_per_file if i < n_files - 1 else len(optimized_predictions)

                file_preds = optimized_predictions[start_idx:end_idx]
                file_confs = optimized_confidences[start_idx:end_idx]

                if file_preds:
                    pred_counts = Counter(file_preds)
                    final_pred = pred_counts.most_common(1)[0][0]
                    pred_mask = np.array(file_preds) == final_pred
                    final_conf = np.mean([file_confs[j] for j in range(len(file_confs)) if pred_mask[j]])

                    file_predictions[file_name] = {
                        'prediction': final_pred,
                        'confidence': final_conf,
                        'sample_count': len(file_preds)
                    }

        self.final_predictions = file_predictions
        print(f"\n文件级预测完成，共 {len(file_predictions)} 个文件")
        return file_predictions

    def comprehensive_visualization(self):
        """Enhanced comprehensive visualization with scatter plots, violin plots, and more"""
        print("\n" + "=" * 60)
        print("Enhanced Comprehensive Visualization")
        print("=" * 60)

        if not self.final_predictions:
            print("No prediction results for visualization")
            return

        # Set modern styling
        plt.style.use('default')
        plt.rcParams.update({
            'font.size': 11,
            'font.family': 'DejaVu Sans',
            'axes.labelsize': 12,
            'axes.titlesize': 14,
            'xtick.labelsize': 10,
            'ytick.labelsize': 10,
            'legend.fontsize': 10,
            'figure.titlesize': 16,
            'axes.grid': True,
            'grid.alpha': 0.3,
            'axes.spines.top': False,
            'axes.spines.right': False,
            'axes.axisbelow': True
        })

        # Create comprehensive chart layout - 3x3 grid
        fig = plt.figure(figsize=(24, 18))
        fig.patch.set_facecolor('#f8f9fa')

        # Enhanced grid layout
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.25,
                              left=0.06, right=0.96, top=0.92, bottom=0.06)

        # Prepare data
        file_names = list(self.final_predictions.keys())
        predictions = [self.final_predictions[f]['prediction'] for f in file_names]
        confidences = [self.final_predictions[f]['confidence'] for f in file_names]
        sample_counts = [self.final_predictions[f].get('sample_count', 0) for f in file_names]
        vote_ratios = [self.final_predictions[f].get('vote_ratio', 1.0) for f in file_names]

        # Modern color scheme
        colors = {
            'IR': '#FF6B9D',  # Pink
            'OR': '#4ECDC4',  # Teal
            'B': '#45B7D1',  # Sky blue
            'default': '#95A5A6'
        }

        # ========== Subplot 1: Fault Type Distribution (Pie Chart) ==========
        ax1 = fig.add_subplot(gs[0, 0])
        pred_counts = Counter(predictions)
        pie_colors = [colors.get(k, colors['default']) for k in pred_counts.keys()]

        wedges, texts, autotexts = ax1.pie(
            pred_counts.values(),
            labels=pred_counts.keys(),
            colors=pie_colors,
            autopct='%1.1f%%',
            startangle=90,
            explode=[0.05] * len(pred_counts),
            shadow=True,
            textprops={'fontsize': 11, 'fontweight': 'bold'}
        )

        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')

        ax1.set_title('Fault Type Distribution', fontweight='bold', fontsize=14,
                      color='#2c3e50', pad=15)

        # ========== Subplot 2: Confidence Distribution (Histogram) ==========
        ax2 = fig.add_subplot(gs[0, 1])

        n, bins, patches = ax2.hist(confidences, bins=12, alpha=0.8,
                                    color='#74b9ff', edgecolor='white', linewidth=1.2)

        for i, patch in enumerate(patches):
            height = patch.get_height()
            intensity = height / max(n)
            color = plt.cm.viridis(intensity * 0.8 + 0.2)
            patch.set_facecolor(color)

        mean_conf = np.mean(confidences)
        ax2.axvline(mean_conf, color='#e74c3c', linestyle='--', linewidth=2,
                    label=f'Mean: {mean_conf:.3f}')

        ax2.set_title('Confidence Distribution', fontweight='bold', fontsize=14,
                      color='#2c3e50', pad=15)
        ax2.set_xlabel('Confidence Score', fontweight='bold')
        ax2.set_ylabel('Frequency', fontweight='bold')
        ax2.legend()

        # ========== Subplot 3: Confidence vs Sample Count (Scatter Plot) ==========
        ax3 = fig.add_subplot(gs[0, 2])

        # Create scatter plot with different colors for different fault types
        for fault_type in set(predictions):
            mask = np.array(predictions) == fault_type
            x_data = np.array(sample_counts)[mask]
            y_data = np.array(confidences)[mask]

            ax3.scatter(x_data, y_data,
                        c=colors.get(fault_type, colors['default']),
                        label=fault_type, alpha=0.7, s=60,
                        edgecolors='white', linewidth=1)

        # Add trend line
        if sample_counts and confidences:
            z = np.polyfit(sample_counts, confidences, 1)
            p = np.poly1d(z)
            ax3.plot(sample_counts, p(sample_counts), "r--", alpha=0.8, linewidth=2)

        ax3.set_title('Confidence vs Sample Count Relationship', fontweight='bold',
                      fontsize=14, color='#2c3e50', pad=15)
        ax3.set_xlabel('Sample Count per File', fontweight='bold')
        ax3.set_ylabel('Prediction Confidence', fontweight='bold')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # ========== Subplot 4: File-wise Confidence Bar Chart ==========
        ax4 = fig.add_subplot(gs[1, 0])

        file_indices = range(len(file_names))
        bar_colors = [colors.get(pred, colors['default']) for pred in predictions]

        bars = ax4.bar(file_indices, confidences, color=bar_colors, alpha=0.8,
                       edgecolor='white', linewidth=1.2)

        # Add confidence threshold lines
        ax4.axhline(y=0.8, color='#e67e22', linestyle=':', linewidth=2,
                    alpha=0.8, label='High Confidence (0.8)')
        ax4.axhline(y=0.6, color='#f39c12', linestyle=':', linewidth=2,
                    alpha=0.8, label='Medium Confidence (0.6)')

        ax4.set_title('File-wise Prediction Confidence', fontweight='bold',
                      fontsize=14, color='#2c3e50', pad=15)
        ax4.set_xlabel('File Index', fontweight='bold')
        ax4.set_ylabel('Confidence Score', fontweight='bold')

        step = max(1, len(file_names) // 10)
        ax4.set_xticks(range(0, len(file_names), step))
        ax4.legend()

        # ========== Subplot 5: Confidence Violin Plot by Fault Type ==========
        ax5 = fig.add_subplot(gs[1, 1])

        # Prepare data for violin plot
        fault_types = list(set(predictions))
        conf_by_fault = []

        for fault in fault_types:
            fault_confidences = [confidences[i] for i, pred in enumerate(predictions) if pred == fault]
            conf_by_fault.append(fault_confidences)

        # Create violin plot
        parts = ax5.violinplot(conf_by_fault, positions=range(len(fault_types)),
                               showmeans=True, showmedians=True, showextrema=True)

        # Customize violin plot colors
        for i, pc in enumerate(parts['bodies']):
            fault_type = fault_types[i]
            pc.set_facecolor(colors.get(fault_type, colors['default']))
            pc.set_alpha(0.7)

        # Customize other elements
        parts['cmeans'].set_color('#2c3e50')
        parts['cmedians'].set_color('#e74c3c')
        parts['cmaxes'].set_color('#34495e')
        parts['cmins'].set_color('#34495e')
        parts['cbars'].set_color('#34495e')

        ax5.set_title('Confidence Distribution by Fault Type', fontweight='bold',
                      fontsize=14, color='#2c3e50', pad=15)
        ax5.set_xlabel('Fault Type', fontweight='bold')
        ax5.set_ylabel('Confidence Score', fontweight='bold')
        ax5.set_xticks(range(len(fault_types)))
        ax5.set_xticklabels(fault_types)
        ax5.grid(True, alpha=0.3)

        # ========== Subplot 6: Vote Ratio vs Confidence (Scatter Plot) ==========
        ax6 = fig.add_subplot(gs[1, 2])

        for fault_type in set(predictions):
            mask = np.array(predictions) == fault_type
            x_data = np.array(vote_ratios)[mask]
            y_data = np.array(confidences)[mask]

            ax6.scatter(x_data, y_data,
                        c=colors.get(fault_type, colors['default']),
                        label=fault_type, alpha=0.7, s=60,
                        edgecolors='white', linewidth=1)

        # Add diagonal reference line
        ax6.plot([0, 1], [0, 1], 'k--', alpha=0.3, linewidth=1)

        ax6.set_title('Vote Ratio vs Confidence Correlation', fontweight='bold',
                      fontsize=14, color='#2c3e50', pad=15)
        ax6.set_xlabel('Vote Ratio', fontweight='bold')
        ax6.set_ylabel('Prediction Confidence', fontweight='bold')
        ax6.legend()
        ax6.grid(True, alpha=0.3)
        ax6.set_xlim(0, 1.05)
        ax6.set_ylim(0, 1.05)

        # ========== Subplot 7: Domain Gap Analysis ==========
        ax7 = fig.add_subplot(gs[2, 0])

        if self.domain_gap_analysis:
            mmd_dist = self.domain_gap_analysis.get('mmd_distance', 0)
            avg_overlap = self.domain_gap_analysis.get('overlap_analysis', {}).get('average_overlap', 0)

            gap_metrics = {
                'MMD Distance': min(1.0, mmd_dist * 100),
                'Distribution Overlap': avg_overlap,
                'Mean Confidence': np.mean(confidences),
                'Prediction Consistency': 1 - (len(set(predictions)) - 1) / len(self.target_fault_types)
            }

            metric_names = list(gap_metrics.keys())
            metric_values = list(gap_metrics.values())

            bar_colors_gradient = plt.cm.RdYlBu_r(np.linspace(0.2, 0.8, len(metric_names)))

            bars = ax7.barh(metric_names, metric_values, color=bar_colors_gradient,
                            alpha=0.8, edgecolor='white', linewidth=2)

            for i, (bar, value) in enumerate(zip(bars, metric_values)):
                width = bar.get_width()
                ax7.text(width + 0.01, bar.get_y() + bar.get_height() / 2,
                         f'{value:.3f}', ha='left', va='center',
                         fontweight='bold', fontsize=10, color='#2c3e50')

            ax7.set_xlim(0, 1.1)
            ax7.set_title('Domain Adaptation Metrics', fontweight='bold',
                          fontsize=14, color='#2c3e50', pad=15)
            ax7.set_xlabel('Metric Values', fontweight='bold')
            ax7.axvline(x=0.5, color='#95a5a6', linestyle='--', alpha=0.7)
            ax7.axvline(x=0.8, color='#27ae60', linestyle='--', alpha=0.7)
        else:
            ax7.text(0.5, 0.5, 'Domain Gap Analysis\nData Unavailable',
                     ha='center', va='center', transform=ax7.transAxes,
                     fontsize=12, color='#7f8c8d', style='italic')
            ax7.set_title('Domain Adaptation Metrics', fontweight='bold',
                          fontsize=14, color='#2c3e50', pad=15)

        # ========== Subplot 8: Prediction Certainty Heatmap ==========
        ax8 = fig.add_subplot(gs[2, 1])

        # Create heatmap data: confidence matrix by file and fault type
        fault_types_sorted = sorted(set(predictions))
        n_files = min(16, len(file_names))  # Limit for visualization

        heatmap_data = np.zeros((len(fault_types_sorted), n_files))

        for i, fault in enumerate(fault_types_sorted):
            file_indices_for_fault = [j for j, pred in enumerate(predictions[:n_files]) if pred == fault]
            for j in file_indices_for_fault:
                heatmap_data[i, j] = confidences[j]

        im = ax8.imshow(heatmap_data, cmap='RdYlBu_r', aspect='auto', vmin=0, vmax=1)

        ax8.set_title('Prediction Confidence Heatmap', fontweight='bold',
                      fontsize=14, color='#2c3e50', pad=15)
        ax8.set_xlabel('File Index', fontweight='bold')
        ax8.set_ylabel('Fault Type', fontweight='bold')
        ax8.set_yticks(range(len(fault_types_sorted)))
        ax8.set_yticklabels(fault_types_sorted)

        # Add colorbar
        cbar = plt.colorbar(im, ax=ax8, shrink=0.8)
        cbar.set_label('Confidence Score', fontweight='bold')

        # ========== Subplot 9: Statistical Summary ==========
        ax9 = fig.add_subplot(gs[2, 2])

        # Create statistical summary
        stats_data = {
            'Total Files': len(self.final_predictions),
            'Unique Faults': len(set(predictions)),
            'Max Confidence': f"{max(confidences):.3f}",
            'Min Confidence': f"{min(confidences):.3f}",
            'Std Confidence': f"{np.std(confidences):.3f}",
            'High Conf (>0.8)': f"{sum(1 for c in confidences if c > 0.8)}"
        }

        y_pos = np.arange(len(stats_data))
        stat_names = list(stats_data.keys())
        stat_values = list(stats_data.values())

        # Create text-based summary
        ax9.axis('off')
        ax9.set_title('Statistical Summary', fontweight='bold',
                      fontsize=14, color='#2c3e50', pad=15)

        for i, (name, value) in enumerate(stats_data.items()):
            y_position = 0.9 - i * 0.15
            ax9.text(0.05, y_position, f'{name}:', fontweight='bold',
                     fontsize=11, transform=ax9.transAxes, color='#2c3e50')
            ax9.text(0.6, y_position, str(value), fontsize=11,
                     transform=ax9.transAxes, color='#34495e')

        # Add prediction distribution
        ax9.text(0.05, 0.25, 'Fault Distribution:', fontweight='bold',
                 fontsize=11, transform=ax9.transAxes, color='#2c3e50')

        for i, (fault, count) in enumerate(Counter(predictions).items()):
            y_pos = 0.1 - i * 0.08
            percentage = count / len(predictions) * 100
            ax9.text(0.1, y_pos, f'{fault}: {count} ({percentage:.1f}%)',
                     fontsize=10, transform=ax9.transAxes, color='#34495e')

        # ========== Main Title and Final Styling ==========
        fig.suptitle('Transfer Learning Fault Diagnosis - Comprehensive Analysis Dashboard',
                     fontsize=18, fontweight='bold', color='#2c3e50', y=0.96)

        # Add subtitle with key metrics
        high_conf_count = sum(1 for c in confidences if c > 0.8)
        subtitle = f'Files: {len(self.final_predictions)} | Avg Confidence: {np.mean(confidences):.3f} | High Confidence Files: {high_conf_count} | Fault Types: {len(set(predictions))}'
        fig.text(0.5, 0.93, subtitle, ha='center', fontsize=11,
                 color='#7f8c8d', style='italic')

        # Save high-quality images
        plt.savefig(f'{self.source_data_path}/enhanced_transfer_learning_dashboard.png',
                    dpi=300, bbox_inches='tight', facecolor='#f8f9fa',
                    edgecolor='none', format='png')

        plt.savefig(f'{self.source_data_path}/enhanced_transfer_learning_dashboard.pdf',
                    bbox_inches='tight', facecolor='#f8f9fa',
                    edgecolor='none', format='pdf')

        plt.show()
    def generate_final_report(self):
        """生成最终诊断报告"""
        print("\n" + "=" * 80)
        print("迁移学习故障诊断最终报告")
        print("=" * 80)

        if not self.final_predictions:
            print("没有预测结果生成报告")
            return

        # 统计分析
        predictions = [result['prediction'] for result in self.final_predictions.values()]
        confidences = [result['confidence'] for result in self.final_predictions.values()]

        pred_counts = Counter(predictions)
        max_confidence = max(confidences)
        avg_confidence = np.mean(confidences)

        print(f"\n=== 诊断结果总览 ===")
        print(f"分析文件总数: {len(self.final_predictions)}")
        print(f"最高置信度: {max_confidence:.4f}")
        print(f"平均置信度: {avg_confidence:.4f}")

        print(f"\n=== 故障类型分布 ===")
        for fault in self.target_fault_types:
            count = pred_counts.get(fault, 0)
            ratio = count / len(predictions) * 100
            print(f"{fault}: {count:2d} 个文件 ({ratio:5.1f}%)")

        # 详细结果
        print(f"\n=== 各文件详细诊断结果 ===")
        print("-" * 70)
        print(f"{'文件':<8} {'故障类型':<8} {'置信度':<10} {'样本数':<8}")
        print("-" * 70)

        # 按置信度排序
        sorted_files = sorted(self.final_predictions.items(),
                              key=lambda x: x[1]['confidence'], reverse=True)

        for file_name, result in sorted_files:
            file_id = file_name.replace('.mat', '')
            pred = result['prediction']
            conf = result['confidence']
            sample_count = result.get('sample_count', 'N/A')
            print(f"{file_id:<8} {pred:<8} {conf:<10.4f} {sample_count:<8}")

        results_data = []
        for file_name, result in self.final_predictions.items():
            results_data.append({
                'File_Name': file_name,
                'Bearing_ID': file_name.replace('.mat', ''),
                'Fault_Type': result['prediction'],
                'Confidence': result['confidence'],
                'Sample_Count': result.get('sample_count', 0),
                'Vote_Ratio': result.get('vote_ratio', 1.0)
            })

        results_df = pd.DataFrame(results_data)
        results_df.to_csv(f'{self.source_data_path}/transfer_learning_diagnosis_results.csv', index=False)

        print(f"\n=== 域适应效果评估 ===")
        if self.domain_gap_analysis:
            mmd_dist = self.domain_gap_analysis.get('mmd_distance', 0)
            avg_overlap = self.domain_gap_analysis.get('overlap_analysis', {}).get('average_overlap', 0)

        high_conf_count = sum(1 for c in confidences if c > 0.8)
        return results_df


def main():
    """主函数"""
    print("=" * 80)
    print("迁移学习故障诊断系统")

    try:
        # 初始化系统
        diagnosis_system = TransferLearningDiagnosisSystem(
            source_data_path='processed_data_mixed_fs',
            target_data_path='processed_target_data'
        )

        # 步骤1: 加载源域模型和数据
        print("\n步骤1: 加载源域模型和数据")
        diagnosis_system.load_source_model_and_data()

        # 步骤2: 加载目标域数据
        print("\n步骤2: 加载目标域数据")
        diagnosis_system.load_target_domain_data()

        # 步骤3: 深度域间差异分析
        print("\n步骤3: 深度域间差异分析")
        diagnosis_system.deep_domain_gap_analysis()

        # 步骤4: 多策略域适应
        print("\n步骤4: 多策略域适应")
        diagnosis_system.multi_strategy_domain_adaptation()

        # 步骤5: 文件级聚合和最终预测
        print("\n步骤5: 文件级聚合和最终预测")
        diagnosis_system.file_level_aggregation_and_prediction()

        # 步骤6: 综合可视化
        print("\n步骤6: 综合可视化")
        diagnosis_system.comprehensive_visualization()

        # 步骤7: 生成最终报告
        print("\n步骤7: 生成最终报告")
        diagnosis_system.generate_final_report()

        print(f"\n迁移学习故障诊断完成！")

        # 显示关键结果
        if diagnosis_system.final_predictions:
            confidences = [result['confidence'] for result in diagnosis_system.final_predictions.values()]
            predictions = [result['prediction'] for result in diagnosis_system.final_predictions.values()]

            print(f"\n=== 关键结果摘要 ===")
            print(f"最高置信度: {max(confidences):.4f}")
            print(f"平均置信度: {np.mean(confidences):.4f}")

            pred_counts = Counter(predictions)
            print(f"故障分布: {dict(pred_counts)}")

            high_conf_count = sum(1 for c in confidences if c > 0.8)

    except Exception as e:
        print(f"执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()


