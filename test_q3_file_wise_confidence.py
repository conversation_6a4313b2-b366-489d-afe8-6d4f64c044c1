#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Q3 file-wise confidence chart with x-axis starting from 1
"""

import numpy as np
import matplotlib.pyplot as plt
import os
from collections import Counter

# Set font configuration
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

class TestQ3FileWiseConfidence:
    def __init__(self):
        self.output_dir = os.path.join(os.getcwd(), "图片Q3_FileWise_Test")
        self.colors_palette = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA", "#D25F0D", "#E9BD0D"]
        
    def save_figure(self, fig, filename):
        """Save figure to specified directory"""
        os.makedirs(self.output_dir, exist_ok=True)
        filepath = os.path.join(self.output_dir, filename)
        fig.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close(fig)
        print(f"Image saved: {filepath}")

    def test_file_wise_confidence(self):
        """Test file-wise confidence chart with x-axis starting from 1"""
        print("Testing Q3 file-wise confidence chart with x-axis starting from 1...")
        
        # Mock data
        np.random.seed(42)
        n_files = 16
        
        # Mock file names and predictions
        file_names = [f'File_{i+1}' for i in range(n_files)]
        fault_types = ['IR', 'OR', 'B']
        predictions = np.random.choice(fault_types, n_files)
        confidences = np.random.beta(2, 1, n_files)  # Beta distribution favoring high confidence
        
        # Color mapping
        colors = {
            'IR': self.colors_palette[0],
            'OR': self.colors_palette[1], 
            'B': self.colors_palette[2],
            'default': '#95A5A6'
        }
        
        # ========== Chart 4: File-wise Confidence Bar Chart ==========
        print("Generating Chart 4: File-wise Confidence Bar Chart...")
        fig4 = plt.figure(figsize=(12, 6))
        fig4.patch.set_facecolor('#f8f9fa')

        # 使用从1开始的文件索引
        file_indices = range(1, len(file_names) + 1)
        bar_colors = [colors.get(pred, colors['default']) for pred in predictions]

        bars = plt.bar(file_indices, confidences, color=bar_colors, alpha=0.8,
                       edgecolor='white', linewidth=1.2)

        # Add confidence threshold lines
        plt.axhline(y=0.8, color=self.colors_palette[4], linestyle=':', linewidth=2,
                    alpha=0.8, label='High Confidence (0.8)')
        plt.axhline(y=0.6, color=self.colors_palette[5], linestyle=':', linewidth=2,
                    alpha=0.8, label='Medium Confidence (0.6)')

        plt.title('File-wise Prediction Confidence', fontweight='bold',
                  fontsize=14, color='#2c3e50', pad=15)
        plt.xlabel('File Index', fontweight='bold')
        plt.ylabel('Confidence Score', fontweight='bold')

        step = max(1, len(file_names) // 10)
        plt.xticks(range(1, len(file_names) + 1, step))
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        self.save_figure(fig4, '04_file_wise_confidence.png')
        
        # Print some verification info
        print(f"File indices range: {min(file_indices)} to {max(file_indices)}")
        print(f"X-axis ticks: {list(range(1, len(file_names) + 1, step))}")
        print(f"Number of files: {len(file_names)}")
        
        print(f"\nTest completed! Image saved to: {self.output_dir}")

if __name__ == "__main__":
    test = TestQ3FileWiseConfidence()
    test.test_file_wise_confidence()
