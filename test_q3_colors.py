#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Q3颜色修改的简单脚本
"""

import matplotlib.pyplot as plt
import numpy as np
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def test_q3_colors():
    """测试Q3使用的颜色配置"""
    
    # Q1的调色盘
    colors_palette = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA", "#D25F0D", "#E9BD0D"]
    fault_types_list = ['IR', 'OR', 'B']
    colors = {
        fault_types_list[i]: colors_palette[i] for i in range(len(fault_types_list))
    }
    colors['default'] = '#95A5A6'
    
    print("Q3使用的颜色配置:")
    for fault_type, color in colors.items():
        print(f"  {fault_type}: {color}")
    
    # 创建测试图表
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 测试饼图颜色
    ax1 = axes[0]
    pred_counts = {'IR': 5, 'OR': 3, 'B': 2}
    pie_colors = [colors.get(k, colors['default']) for k in pred_counts.keys()]
    
    wedges, texts, autotexts = ax1.pie(
        pred_counts.values(),
        labels=pred_counts.keys(),
        colors=pie_colors,
        autopct='%1.1f%%',
        startangle=90
    )
    ax1.set_title('故障类型分布 (使用Q1调色盘)', fontweight='bold')
    
    # 测试直方图颜色
    ax2 = axes[1]
    confidences = np.random.beta(2, 1, 100)  # 模拟置信度数据
    n, bins, patches = ax2.hist(confidences, bins=12, alpha=0.8,
                                color=colors_palette[0], edgecolor='white', linewidth=1.2)
    
    for i, patch in enumerate(patches):
        height = patch.get_height()
        intensity = height / max(n)
        base_color = colors_palette[i % len(colors_palette)]
        patch.set_facecolor(base_color)
        patch.set_alpha(0.7 + intensity * 0.3)
    
    mean_conf = np.mean(confidences)
    ax2.axvline(mean_conf, color=colors_palette[2], linestyle='--', linewidth=2,
                label=f'Mean: {mean_conf:.3f}')
    ax2.set_title('置信度分布', fontweight='bold')
    ax2.legend()
    
    # 测试条形图颜色
    ax3 = axes[2]
    metrics = ['Metric1', 'Metric2', 'Metric3', 'Metric4']
    values = [0.8, 0.6, 0.9, 0.7]
    bar_colors = [colors_palette[i % len(colors_palette)] for i in range(len(metrics))]
    
    bars = ax3.bar(metrics, values, color=bar_colors, alpha=0.8, edgecolor='white', linewidth=2)
    ax3.set_title('指标对比', fontweight='bold')
    ax3.set_ylabel('数值')
    
    plt.tight_layout()
    
    # 保存测试图片
    output_dir = os.path.join(os.getcwd(), "图片Q3")
    os.makedirs(output_dir, exist_ok=True)
    filepath = os.path.join(output_dir, 'color_test.png')
    fig.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.close(fig)
    print(f"测试图像已保存: {filepath}")

if __name__ == "__main__":
    test_q3_colors()
