#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Q3可视化功能的简单脚本
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
from collections import Counter

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

class TestQ3Visualization:
    def __init__(self):
        self.output_dir = os.path.join(os.getcwd(), "图片Q3_测试")
        
    def save_figure(self, fig, filename):
        """保存图片到指定目录"""
        os.makedirs(self.output_dir, exist_ok=True)
        filepath = os.path.join(self.output_dir, filename)
        fig.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close(fig)
        print(f"图像已保存: {filepath}")

    def test_visualization(self):
        """测试可视化功能"""
        print("开始测试Q3可视化功能...")
        
        # 模拟数据
        np.random.seed(42)
        n_files = 16
        
        # 模拟预测结果
        fault_types = ['IR', 'OR', 'B']
        predictions = np.random.choice(fault_types, n_files)
        confidences = np.random.beta(2, 1, n_files)  # 偏向高置信度的分布
        sample_counts = np.random.randint(50, 200, n_files)
        vote_ratios = np.random.beta(3, 1, n_files)  # 偏向高投票率的分布
        
        # 使用与Q1相同的调色盘
        colors_palette = ['#FF6B6B', "#4E16B0", "#D40E4D", "#DA12DA", "#D25F0D", "#E9BD0D"]
        fault_types_list = ['IR', 'OR', 'B']
        colors = {
            fault_types_list[i]: colors_palette[i] for i in range(len(fault_types_list))
        }
        colors['default'] = '#95A5A6'
        
        # 图1: 故障类型分布饼图
        print("生成图1: 故障类型分布饼图...")
        fig1 = plt.figure(figsize=(8, 6))
        fig1.patch.set_facecolor('#f8f9fa')
        
        pred_counts = Counter(predictions)
        pie_colors = [colors.get(k, colors['default']) for k in pred_counts.keys()]
        
        wedges, texts, autotexts = plt.pie(
            pred_counts.values(),
            labels=pred_counts.keys(),
            colors=pie_colors,
            autopct='%1.1f%%',
            startangle=90,
            explode=[0.05] * len(pred_counts),
            shadow=True,
            textprops={'fontsize': 11, 'fontweight': 'bold'}
        )
        
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
        
        plt.title('故障类型分布', fontweight='bold', fontsize=14,
                  color='#2c3e50', pad=15)
        self.save_figure(fig1, '01_fault_type_distribution.png')
        
        # 图2: 置信度分布直方图
        print("生成图2: 置信度分布直方图...")
        fig2 = plt.figure(figsize=(8, 6))
        fig2.patch.set_facecolor('#f8f9fa')
        
        n, bins, patches = plt.hist(confidences, bins=12, alpha=0.8,
                                    color=colors_palette[0], edgecolor='white', linewidth=1.2)
        
        for i, patch in enumerate(patches):
            height = patch.get_height()
            intensity = height / max(n)
            base_color = colors_palette[i % len(colors_palette)]
            patch.set_facecolor(base_color)
            patch.set_alpha(0.7 + intensity * 0.3)
        
        mean_conf = np.mean(confidences)
        plt.axvline(mean_conf, color=colors_palette[2], linestyle='--', linewidth=2,
                    label=f'Mean: {mean_conf:.3f}')
        
        plt.title('置信度分布', fontweight='bold', fontsize=14,
                  color='#2c3e50', pad=15)
        plt.xlabel('置信度分数', fontweight='bold')
        plt.ylabel('频次', fontweight='bold')
        plt.legend()
        plt.grid(True, alpha=0.3)
        self.save_figure(fig2, '02_confidence_distribution.png')
        
        # 图3: 置信度与样本数关系散点图
        print("生成图3: 置信度与样本数关系散点图...")
        fig3 = plt.figure(figsize=(8, 6))
        fig3.patch.set_facecolor('#f8f9fa')
        
        for fault_type in set(predictions):
            mask = np.array(predictions) == fault_type
            x_data = np.array(sample_counts)[mask]
            y_data = np.array(confidences)[mask]
            
            plt.scatter(x_data, y_data,
                        c=colors.get(fault_type, colors['default']),
                        label=fault_type, alpha=0.7, s=60,
                        edgecolors='white', linewidth=1)
        
        # Add trend line
        if len(sample_counts) > 1 and len(confidences) > 1:
            z = np.polyfit(sample_counts, confidences, 1)
            p = np.poly1d(z)
            plt.plot(sample_counts, p(sample_counts), "r--", alpha=0.8, linewidth=2)
        
        plt.title('置信度与样本数关系', fontweight='bold',
                  fontsize=14, color='#2c3e50', pad=15)
        plt.xlabel('每文件样本数', fontweight='bold')
        plt.ylabel('预测置信度', fontweight='bold')
        plt.legend()
        plt.grid(True, alpha=0.3)
        self.save_figure(fig3, '03_confidence_vs_sample_count.png')
        
        print(f"\n测试完成！图片保存在: {self.output_dir}")

if __name__ == "__main__":
    test = TestQ3Visualization()
    test.test_visualization()
